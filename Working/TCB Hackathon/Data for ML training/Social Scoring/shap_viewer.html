<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SHAP Analysis Results - Social Scoring System</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #1f77b4;
        }
        
        .header h1 {
            color: #1f77b4;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .metric-card h3 {
            margin: 0 0 10px 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .metric-card .value {
            font-size: 2rem;
            font-weight: bold;
            margin: 0;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section h2 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }
        
        .image-container {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
        }
        
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .image-container h3 {
            color: #555;
            margin-bottom: 15px;
        }
        
        .insights {
            background-color: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #28a745;
        }
        
        .insights h3 {
            color: #28a745;
            margin-top: 0;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .feature-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        
        .feature-item strong {
            color: #1f77b4;
        }
        
        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .navigation a {
            display: block;
            color: #1f77b4;
            text-decoration: none;
            margin: 5px 0;
            padding: 5px 10px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .navigation a:hover {
            background-color: #f0f0f0;
        }
        
        .dashboard-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            margin: 20px 0;
            font-weight: bold;
            transition: transform 0.3s;
        }
        
        .dashboard-link:hover {
            transform: translateY(-2px);
            color: white;
        }
    </style>
</head>
<body>
    <div class="navigation">
        <strong>Quick Navigation</strong>
        <a href="#overview">Overview</a>
        <a href="#feature-importance">Feature Importance</a>
        <a href="#summary-plot">Summary Plot</a>
        <a href="#individual">Individual Examples</a>
        <a href="#insights">Key Insights</a>
    </div>

    <div class="container">
        <div class="header">
            <h1>🧠 SHAP Analysis Results</h1>
            <p>Explainable AI for Social Scoring System</p>
            <a href="http://localhost:8501" class="dashboard-link" target="_blank">
                🚀 Open Interactive Dashboard
            </a>
        </div>

        <section id="overview" class="section">
            <h2>📊 Model Performance Overview</h2>
            <div class="metrics">
                <div class="metric-card">
                    <h3>R² Score</h3>
                    <p class="value">0.997</p>
                </div>
                <div class="metric-card">
                    <h3>RMSE</h3>
                    <p class="value">1.145</p>
                </div>
                <div class="metric-card">
                    <h3>Features Used</h3>
                    <p class="value">20</p>
                </div>
                <div class="metric-card">
                    <h3>Test Samples</h3>
                    <p class="value">10,000</p>
                </div>
            </div>
            
            <div class="insights">
                <h3>🎯 Model Performance Summary</h3>
                <p>Our Random Forest model achieves exceptional performance with an R² score of <strong>0.997</strong>, 
                indicating that the model explains 99.7% of the variance in social scores. The low RMSE of 1.145 
                demonstrates high prediction accuracy.</p>
                
                <p>This high performance validates that our social scoring algorithm is highly predictable and 
                consistent, making it reliable for real-world applications in DeFi, NFT marketplaces, and 
                airdrop distributions.</p>
            </div>
        </section>

        <section id="feature-importance" class="section">
            <h2>📈 Feature Importance Analysis</h2>
            <div class="image-container">
                <h3>Most Important Features (SHAP Values)</h3>
                <img src="shap_feature_importance.png" alt="SHAP Feature Importance" />
            </div>
            
            <div class="insights">
                <h3>🔍 Key Findings</h3>
                <div class="feature-list">
                    <div class="feature-item">
                        <strong>Engagement Score:</strong> The most influential factor, directly reflecting user interaction and content quality.
                    </div>
                    <div class="feature-item">
                        <strong>Activity Score:</strong> Second most important, measuring consistent platform usage and posting patterns.
                    </div>
                    <div class="feature-item">
                        <strong>Quality Score:</strong> Third in importance, capturing account verification and profile completeness.
                    </div>
                    <div class="feature-item">
                        <strong>Influence Score:</strong> Fourth factor, measuring follower relationships and social influence.
                    </div>
                </div>
            </div>
        </section>

        <section id="summary-plot" class="section">
            <h2>🎨 SHAP Summary Plot</h2>
            <div class="image-container">
                <h3>Feature Impact Distribution</h3>
                <img src="shap_summary_plot.png" alt="SHAP Summary Plot" />
            </div>
            
            <div class="insights">
                <h3>📊 Summary Plot Interpretation</h3>
                <p>The summary plot shows how each feature impacts the model's predictions:</p>
                <ul>
                    <li><strong>Red dots:</strong> High feature values</li>
                    <li><strong>Blue dots:</strong> Low feature values</li>
                    <li><strong>X-axis:</strong> SHAP value (impact on prediction)</li>
                    <li><strong>Positive SHAP values:</strong> Increase the social score</li>
                    <li><strong>Negative SHAP values:</strong> Decrease the social score</li>
                </ul>
            </div>
        </section>

        <section id="individual" class="section">
            <h2>👤 Individual User Explanations</h2>
            
            <div class="image-container">
                <h3>User Example 1 - Explanation</h3>
                <img src="shap_individual_0.png" alt="Individual SHAP Explanation 1" />
            </div>
            
            <div class="image-container">
                <h3>User Example 2 - Explanation</h3>
                <img src="shap_individual_10.png" alt="Individual SHAP Explanation 2" />
            </div>
            
            <div class="image-container">
                <h3>User Example 3 - Explanation</h3>
                <img src="shap_individual_50.png" alt="Individual SHAP Explanation 3" />
            </div>
            
            <div class="insights">
                <h3>🔬 Individual Analysis Benefits</h3>
                <p>These waterfall plots show exactly why each user received their specific social score:</p>
                <ul>
                    <li><strong>Base Value:</strong> The average prediction across all users</li>
                    <li><strong>Feature Contributions:</strong> How each feature pushes the score up or down</li>
                    <li><strong>Final Prediction:</strong> The resulting social score for that specific user</li>
                </ul>
                <p>This transparency is crucial for building trust in automated scoring systems and 
                ensuring fair treatment of all users.</p>
            </div>
        </section>

        <section id="insights" class="section">
            <h2>💡 Key Business Insights</h2>
            
            <div class="insights">
                <h3>🚀 Actionable Recommendations</h3>
                
                <div class="feature-list">
                    <div class="feature-item">
                        <strong>For DeFi Platforms:</strong>
                        Focus on users with high engagement and quality scores for lending products. 
                        These users show consistent, authentic behavior patterns.
                    </div>
                    
                    <div class="feature-item">
                        <strong>For NFT Marketplaces:</strong>
                        Prioritize users with high influence scores and verified accounts for creator programs. 
                        These users can drive community growth.
                    </div>
                    
                    <div class="feature-item">
                        <strong>For Airdrop Campaigns:</strong>
                        Target users with balanced scores across all dimensions to ensure genuine 
                        community participation and reduce Sybil attacks.
                    </div>
                    
                    <div class="feature-item">
                        <strong>For Risk Management:</strong>
                        Flag users with extremely low quality scores or unusual activity patterns 
                        for additional verification before high-value transactions.
                    </div>
                </div>
            </div>
            
            <div class="insights" style="border-left-color: #dc3545;">
                <h3 style="color: #dc3545;">⚠️ Important Considerations</h3>
                <ul>
                    <li><strong>Fairness:</strong> Regularly audit the model for bias against specific user groups</li>
                    <li><strong>Privacy:</strong> Ensure all data processing complies with privacy regulations</li>
                    <li><strong>Transparency:</strong> Provide users with explanations of their scores when requested</li>
                    <li><strong>Updates:</strong> Retrain the model periodically as social media patterns evolve</li>
                </ul>
            </div>
        </section>

        <section class="section">
            <h2>🔗 Next Steps</h2>
            <div class="insights">
                <h3>🛠️ Implementation Guide</h3>
                <ol>
                    <li><strong>Integration:</strong> Use the provided APIs to integrate scoring into your application</li>
                    <li><strong>Monitoring:</strong> Set up dashboards to monitor score distributions and model performance</li>
                    <li><strong>Feedback Loop:</strong> Collect user feedback to improve the scoring algorithm</li>
                    <li><strong>A/B Testing:</strong> Test different score thresholds for your specific use cases</li>
                    <li><strong>Compliance:</strong> Ensure your implementation meets regulatory requirements</li>
                </ol>
                
                <p style="margin-top: 20px;">
                    <strong>Ready to get started?</strong> 
                    <a href="http://localhost:8501" target="_blank" style="color: #1f77b4;">
                        Open the interactive dashboard
                    </a> to explore the data and test different scenarios.
                </p>
            </div>
        </section>
    </div>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('.navigation a').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            });
        });
        
        // Add loading animation for images
        document.querySelectorAll('img').forEach(img => {
            img.addEventListener('load', function() {
                this.style.opacity = '1';
            });
            img.style.opacity = '0';
            img.style.transition = 'opacity 0.3s';
        });
    </script>
</body>
</html>
