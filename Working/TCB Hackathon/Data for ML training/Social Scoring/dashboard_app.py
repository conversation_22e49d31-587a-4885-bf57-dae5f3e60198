#!/usr/bin/env python3
"""
Interactive Dashboard for Social Scoring and Wallet Linking System
Built with Streamlit for easy deployment and interaction
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import seaborn as sns
import matplotlib.pyplot as plt
from datetime import datetime
import json
import shap
from shap_analysis import SocialScoringExplainer
import io
import base64

# Configure page
st.set_page_config(
    page_title="Social Scoring Dashboard",
    page_icon="🎯",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .score-excellent { color: #28a745; font-weight: bold; }
    .score-good { color: #17a2b8; font-weight: bold; }
    .score-average { color: #ffc107; font-weight: bold; }
    .score-below { color: #fd7e14; font-weight: bold; }
    .score-poor { color: #dc3545; font-weight: bold; }
</style>
""", unsafe_allow_html=True)

@st.cache_data
def load_data():
    """Load all datasets with caching"""
    try:
        social_df = pd.read_csv('twitter_users_with_social_scores.csv')
        wallet_df = pd.read_csv('social_wallet_links.csv')
        combined_df = pd.read_csv('social_scores_with_wallets.csv')
        return social_df, wallet_df, combined_df
    except FileNotFoundError as e:
        st.error(f"Data file not found: {e}")
        st.info("Please run the social scoring system first to generate the required data files.")
        return None, None, None

def get_score_color(score):
    """Get color class based on score"""
    if score >= 80:
        return "score-excellent"
    elif score >= 65:
        return "score-good"
    elif score >= 50:
        return "score-average"
    elif score >= 35:
        return "score-below"
    else:
        return "score-poor"

def create_score_distribution_chart(df):
    """Create score distribution visualization"""
    fig = px.histogram(
        df, 
        x='social_score', 
        nbins=30,
        title='Social Score Distribution',
        labels={'social_score': 'Social Score', 'count': 'Number of Users'},
        color_discrete_sequence=['#1f77b4']
    )
    fig.update_layout(
        xaxis_title="Social Score",
        yaxis_title="Number of Users",
        showlegend=False
    )
    return fig

def create_tier_pie_chart(df):
    """Create score tier pie chart"""
    tier_counts = df['score_tier'].value_counts()
    colors = ['#28a745', '#17a2b8', '#ffc107', '#fd7e14', '#dc3545']
    
    fig = px.pie(
        values=tier_counts.values,
        names=tier_counts.index,
        title='Score Tier Distribution',
        color_discrete_sequence=colors
    )
    return fig

def create_network_distribution_chart(wallet_df):
    """Create network distribution chart"""
    network_counts = wallet_df['network'].value_counts()
    
    fig = px.bar(
        x=network_counts.index,
        y=network_counts.values,
        title='Wallet Network Distribution',
        labels={'x': 'Blockchain Network', 'y': 'Number of Wallets'},
        color=network_counts.values,
        color_continuous_scale='viridis'
    )
    return fig

def create_trust_vs_social_scatter(combined_df):
    """Create trust score vs social score scatter plot"""
    fig = px.scatter(
        combined_df,
        x='social_score',
        y='avg_trust_score',
        color='score_tier',
        size='wallet_count',
        hover_data=['_id', 'followersCount'],
        title='Trust Score vs Social Score',
        labels={
            'social_score': 'Social Score',
            'avg_trust_score': 'Average Trust Score',
            'wallet_count': 'Wallet Count'
        }
    )
    return fig

def create_component_radar_chart(scores):
    """Create radar chart for score components"""
    categories = ['Engagement', 'Influence', 'Quality', 'Activity']
    values = [
        scores['engagement_score'],
        scores['influence_score'], 
        scores['quality_score'],
        scores['activity_score']
    ]
    
    fig = go.Figure()
    
    fig.add_trace(go.Scatterpolar(
        r=values,
        theta=categories,
        fill='toself',
        name='Score Components'
    ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 100]
            )),
        showlegend=False,
        title="Score Component Breakdown"
    )
    
    return fig

@st.cache_resource
def load_shap_explainer():
    """Load and cache SHAP explainer"""
    try:
        explainer = SocialScoringExplainer()
        social_df = pd.read_csv('twitter_users_with_social_scores.csv')
        explainer.train_model(social_df)
        explainer.create_shap_explainer(explainer.scaler.transform(explainer.prepare_features(social_df)))
        return explainer
    except Exception as e:
        st.error(f"Error loading SHAP explainer: {e}")
        return None

def main():
    """Main dashboard application"""

    # Header
    st.markdown('<h1 class="main-header">🎯 Social Scoring Dashboard</h1>', unsafe_allow_html=True)

    # Navigation
    tab1, tab2, tab3, tab4 = st.tabs(["📊 Overview", "🔍 User Analysis", "🧠 SHAP Explainability", "📥 Export"])

    # Load data
    social_df, wallet_df, combined_df = load_data()

    if social_df is None:
        return
    
    with tab1:
        # Sidebar
        st.sidebar.title("🔧 Dashboard Controls")

        # Filters
        st.sidebar.subheader("Filters")

        # Score range filter
        score_range = st.sidebar.slider(
            "Social Score Range",
            min_value=0.0,
            max_value=100.0,
            value=(0.0, 100.0),
            step=1.0
        )

        # Tier filter
        available_tiers = social_df['score_tier'].unique()
        selected_tiers = st.sidebar.multiselect(
            "Score Tiers",
            available_tiers,
            default=available_tiers
        )

        # Network filter (if wallet data available)
        if wallet_df is not None:
            available_networks = wallet_df['network'].unique()
            selected_networks = st.sidebar.multiselect(
                "Blockchain Networks",
                available_networks,
                default=available_networks
            )

        # Apply filters
        filtered_df = social_df[
            (social_df['social_score'] >= score_range[0]) &
            (social_df['social_score'] <= score_range[1]) &
            (social_df['score_tier'].isin(selected_tiers))
        ]
    
    # Main dashboard
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="Total Users",
            value=f"{len(filtered_df):,}",
            delta=f"{len(filtered_df) - len(social_df):,}" if len(filtered_df) != len(social_df) else None
        )
    
    with col2:
        avg_score = filtered_df['social_score'].mean()
        st.metric(
            label="Average Social Score",
            value=f"{avg_score:.1f}",
            delta=f"{avg_score - social_df['social_score'].mean():.1f}" if len(filtered_df) != len(social_df) else None
        )
    
    with col3:
        high_performers = len(filtered_df[filtered_df['social_score'] >= 80])
        st.metric(
            label="High Performers (80+)",
            value=f"{high_performers:,}",
            delta=f"{(high_performers/len(filtered_df)*100):.1f}%" if len(filtered_df) > 0 else "0%"
        )
    
    with col4:
        if wallet_df is not None:
            total_wallets = len(wallet_df[wallet_df['user_id'].isin(filtered_df['_id'].astype(str))])
            st.metric(
                label="Wallet Links",
                value=f"{total_wallets:,}",
                delta=f"{(total_wallets/len(filtered_df)):.1f} avg" if len(filtered_df) > 0 else "0"
            )
    
    # Charts section
    st.markdown("---")
    
    # Row 1: Distribution charts
    col1, col2 = st.columns(2)
    
    with col1:
        score_dist_chart = create_score_distribution_chart(filtered_df)
        st.plotly_chart(score_dist_chart, use_container_width=True)
    
    with col2:
        tier_pie_chart = create_tier_pie_chart(filtered_df)
        st.plotly_chart(tier_pie_chart, use_container_width=True)
    
    # Row 2: Network and correlation charts
    if wallet_df is not None:
        col1, col2 = st.columns(2)
        
        with col1:
            filtered_wallets = wallet_df[wallet_df['network'].isin(selected_networks)]
            network_chart = create_network_distribution_chart(filtered_wallets)
            st.plotly_chart(network_chart, use_container_width=True)
        
        with col2:
            if combined_df is not None:
                filtered_combined = combined_df[
                    (combined_df['social_score'] >= score_range[0]) &
                    (combined_df['social_score'] <= score_range[1]) &
                    (combined_df['score_tier'].isin(selected_tiers))
                ]
                trust_scatter = create_trust_vs_social_scatter(filtered_combined)
                st.plotly_chart(trust_scatter, use_container_width=True)
    
    with tab2:
        st.subheader("🔍 Individual User Analysis")

        # User search
        user_search = st.text_input("Search User by ID:", placeholder="Enter user ID...")

        if user_search:
            user_data = filtered_df[filtered_df['_id'].astype(str).str.contains(user_search, na=False)]

            if not user_data.empty:
                user = user_data.iloc[0]

                col1, col2 = st.columns([2, 1])

                with col1:
                    st.markdown(f"### User: {user['_id']}")
                    st.markdown(f"**Followers:** {user['followersCount']:,}")
                    st.markdown(f"**Bio:** {user.get('rawDescription', 'N/A')[:200]}...")

                    # Score breakdown
                    score_class = get_score_color(user['social_score'])
                    st.markdown(f"**Social Score:** <span class='{score_class}'>{user['social_score']:.1f}</span>", unsafe_allow_html=True)
                    st.markdown(f"**Tier:** {user['score_tier']}")

                    # Component scores
                    st.markdown("**Component Scores:**")
                    st.markdown(f"- Engagement: {user['engagement_score']:.1f}")
                    st.markdown(f"- Influence: {user['influence_score']:.1f}")
                    st.markdown(f"- Quality: {user['quality_score']:.1f}")
                    st.markdown(f"- Activity: {user['activity_score']:.1f}")

                with col2:
                    # Radar chart for components
                    radar_chart = create_component_radar_chart(user)
                    st.plotly_chart(radar_chart, use_container_width=True)

                # Wallet information
                if wallet_df is not None:
                    user_wallets = wallet_df[wallet_df['user_id'] == str(user['_id'])]
                    if not user_wallets.empty:
                        st.markdown("**Linked Wallets:**")
                        for _, wallet in user_wallets.iterrows():
                            st.markdown(f"- {wallet['network'].title()}: `{wallet['wallet_address'][:20]}...` (Trust: {wallet['trust_score']:.1f})")
            else:
                st.warning("User not found in filtered data.")

        # Top performers table
        st.markdown("---")
        st.subheader("🏆 Top Performers")

        top_performers = filtered_df.nlargest(10, 'social_score')[
            ['_id', 'followersCount', 'social_score', 'score_tier', 'engagement_score', 'influence_score']
        ]

        # Format the dataframe for display
        top_performers_display = top_performers.copy()
        top_performers_display['followersCount'] = top_performers_display['followersCount'].apply(lambda x: f"{x:,}")
        top_performers_display['social_score'] = top_performers_display['social_score'].apply(lambda x: f"{x:.1f}")
        top_performers_display['engagement_score'] = top_performers_display['engagement_score'].apply(lambda x: f"{x:.1f}")
        top_performers_display['influence_score'] = top_performers_display['influence_score'].apply(lambda x: f"{x:.1f}")

        st.dataframe(
            top_performers_display,
            column_config={
                "_id": "User ID",
                "followersCount": "Followers",
                "social_score": "Social Score",
                "score_tier": "Tier",
                "engagement_score": "Engagement",
                "influence_score": "Influence"
            },
            use_container_width=True
        )
    
    with tab3:
        st.subheader("🧠 SHAP Explainability Analysis")
        st.markdown("Understanding what drives social scores using SHAP (SHapley Additive exPlanations)")

        # Load SHAP explainer
        with st.spinner("Loading SHAP explainer..."):
            explainer = load_shap_explainer()

        if explainer is not None:
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("### Model Performance")
                if hasattr(explainer, 'y_test') and explainer.y_test is not None:
                    from sklearn.metrics import r2_score, mean_squared_error
                    y_pred = explainer.model.predict(explainer.X_test)
                    r2 = r2_score(explainer.y_test, y_pred)
                    rmse = np.sqrt(mean_squared_error(explainer.y_test, y_pred))

                    st.metric("R² Score", f"{r2:.3f}")
                    st.metric("RMSE", f"{rmse:.3f}")

            with col2:
                st.markdown("### Feature Count")
                st.metric("Total Features", len(explainer.feature_names))
                st.metric("Test Samples", len(explainer.X_test) if explainer.X_test is not None else 0)

            # Feature importance
            if explainer.shap_values is not None:
                st.markdown("### Feature Importance")

                # Calculate feature importance
                feature_importance = np.abs(explainer.shap_values).mean(0)
                importance_df = pd.DataFrame({
                    'feature': explainer.feature_names,
                    'importance': feature_importance
                }).sort_values('importance', ascending=False)

                # Interactive bar chart
                fig_importance = px.bar(
                    importance_df.head(15),
                    x='importance',
                    y='feature',
                    orientation='h',
                    title='Top 15 Most Important Features (SHAP)',
                    labels={'importance': 'Mean |SHAP Value|', 'feature': 'Feature'}
                )
                fig_importance.update_layout(height=600)
                st.plotly_chart(fig_importance, use_container_width=True)

                # SHAP summary plot
                st.markdown("### SHAP Summary Plot")

                # Create matplotlib figure for SHAP
                fig, ax = plt.subplots(figsize=(10, 8))
                shap.summary_plot(
                    explainer.shap_values[:1000],  # Limit for performance
                    explainer.X_test[:1000],
                    feature_names=explainer.feature_names,
                    show=False,
                    ax=ax
                )
                st.pyplot(fig)

                # Individual explanation
                st.markdown("### Individual User Explanation")

                user_idx = st.selectbox(
                    "Select user index for explanation:",
                    range(min(100, len(explainer.X_test))),
                    index=0
                )

                if st.button("Generate Explanation"):
                    # Create waterfall plot
                    fig_waterfall, ax_waterfall = plt.subplots(figsize=(12, 6))

                    # Create SHAP explanation object
                    explanation = shap.Explanation(
                        values=explainer.shap_values[user_idx],
                        base_values=explainer.explainer.expected_value,
                        data=explainer.X_test[user_idx],
                        feature_names=explainer.feature_names
                    )

                    shap.waterfall_plot(explanation, show=False, ax=ax_waterfall)
                    st.pyplot(fig_waterfall)

                    # Show actual vs predicted
                    if hasattr(explainer, 'y_test'):
                        actual_score = explainer.y_test.iloc[user_idx]
                        predicted_score = explainer.model.predict(explainer.X_test[user_idx:user_idx+1])[0]

                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("Actual Score", f"{actual_score:.1f}")
                        with col2:
                            st.metric("Predicted Score", f"{predicted_score:.1f}")
                        with col3:
                            st.metric("Difference", f"{abs(actual_score - predicted_score):.1f}")
        else:
            st.error("Could not load SHAP explainer. Please ensure the model is trained.")

    with tab4:
        st.subheader("📥 Export Data")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("Export Filtered Data"):
                csv = filtered_df.to_csv(index=False)
                st.download_button(
                    label="Download CSV",
                    data=csv,
                    file_name=f"filtered_social_scores_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )

        with col2:
            if st.button("Export Top Performers"):
                csv = top_performers.to_csv(index=False)
                st.download_button(
                    label="Download CSV",
                    data=csv,
                    file_name=f"top_performers_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )

        with col3:
            if wallet_df is not None and st.button("Export Wallet Data"):
                filtered_wallet_data = wallet_df[wallet_df['user_id'].isin(filtered_df['_id'].astype(str))]
                csv = filtered_wallet_data.to_csv(index=False)
                st.download_button(
                    label="Download CSV",
                    data=csv,
                    file_name=f"wallet_links_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )

if __name__ == "__main__":
    main()
