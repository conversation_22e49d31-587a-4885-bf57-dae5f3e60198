# Social Scoring and Wallet Linking System

A comprehensive system for calculating social media scores and linking them to cryptocurrency wallet addresses.

## Overview

This system provides:
1. **Social Scoring**: Multi-dimensional scoring of Twitter users based on engagement, influence, quality, and activity
2. **Wallet Linking**: Connection of social profiles to cryptocurrency wallet addresses across multiple networks
3. **Trust Scoring**: Combined social and verification-based trust metrics

## Components

### 1. Social Scoring Calculator (`social_scoring_calculator.py`)

Calculates comprehensive social scores based on four key dimensions:

#### Scoring Dimensions:
- **Engagement Score (35% weight)**: Followers, likes, tweets, media content
- **Influence Score (25% weight)**: Follower-to-following ratio, list memberships
- **Quality Score (20% weight)**: Verification status, account age, bio completeness
- **Activity Score (20% weight)**: Tweet frequency and engagement patterns

#### Score Tiers:
- **Excellent (80-100)**: High-quality, influential accounts
- **Good (65-79)**: Above-average engagement and quality
- **Average (50-64)**: Typical social media presence
- **Below Average (35-49)**: Limited engagement or newer accounts
- **Poor (0-34)**: Minimal activity or low-quality indicators

### 2. Wallet Linking System (`wallet_linking_system.py`)

Links social profiles to cryptocurrency wallets with verification levels:

#### Supported Networks:
- Ethereum (ETH)
- Bitcoin (BTC)
- Solana (SOL)
- Polygon (MATIC)
- Binance Smart Chain (BNB)
- Cardano (ADA)
- Avalanche (AVAX)

#### Verification Levels:
- **Unverified (0)**: No verification
- **Self-Claimed (1)**: User claims ownership
- **Social Verified (2)**: Extracted from social media bio
- **On-Chain Verified (3)**: Blockchain-based verification
- **KYC Verified (4)**: Identity verification completed

#### Trust Score Calculation:
```
Trust Score = (Social Score / 100) × Verification Multiplier
```

Verification Multipliers:
- Unverified: 0.3
- Self-Claimed: 0.5
- Social Verified: 0.7
- On-Chain Verified: 0.9
- KYC Verified: 1.0

## Usage

### Step 1: Calculate Social Scores

```bash
python social_scoring_calculator.py
```

This will:
- Load the Twitter user data from `twitter_users_social_scoring_50K.csv`
- Calculate social scores for all users
- Save results to `twitter_users_with_social_scores.csv`

### Step 2: Link Wallet Addresses

```bash
python wallet_linking_system.py
```

This will:
- Load the social scoring results
- Extract wallet addresses from user bios
- Generate mock wallet addresses for demonstration
- Create wallet links with trust scores
- Save results to `social_wallet_links.csv` and `social_scores_with_wallets.csv`

### Step 3: Run Complete Analysis

```bash
python run_complete_analysis.py
```

This will run both steps and provide comprehensive analytics.

## Output Files

1. **`twitter_users_with_social_scores.csv`**: Original data + social scores
2. **`social_wallet_links.csv`**: Detailed wallet linking information
3. **`social_scores_with_wallets.csv`**: Combined social and wallet data
4. **`social_scoring_analytics.json`**: Comprehensive analytics report

## Key Features

### Social Scoring Features:
- **Logarithmic Normalization**: Handles extreme values in social metrics
- **Age-Weighted Scoring**: Considers account maturity
- **Anti-Spam Detection**: Penalizes excessive posting
- **Multi-Factor Authentication**: Combines multiple quality indicators

### Wallet Linking Features:
- **Multi-Network Support**: Works with major blockchain networks
- **Bio Extraction**: Automatically finds wallet addresses in user descriptions
- **Confidence Scoring**: Rates the reliability of wallet-profile links
- **Verification Tracking**: Maintains verification status and history

### Trust Scoring Features:
- **Combined Metrics**: Integrates social and verification scores
- **Risk Assessment**: Identifies potentially fraudulent accounts
- **Reputation Tracking**: Builds user reputation over time

## API Integration Examples

### Get User Social Score
```python
from social_scoring_calculator import SocialScoringCalculator

calculator = SocialScoringCalculator()
user_data = {...}  # User's Twitter data
scores = calculator.calculate_social_score(user_data)
print(f"Social Score: {scores['social_score']}")
```

### Link Wallet Address
```python
from wallet_linking_system import WalletLinkingSystem

linking_system = WalletLinkingSystem()
link = linking_system.create_social_wallet_link(
    user_id="*********",
    social_score=75.5,
    wallet_address="******************************************",
    network="ethereum",
    verification_level="social_verified"
)
```

### Validate Wallet Address
```python
is_valid = linking_system.validate_wallet_address(
    "******************************************", 
    "ethereum"
)
```

## Use Cases

### 1. DeFi Lending Platforms
- Assess borrower creditworthiness using social scores
- Verify wallet ownership through social media links
- Adjust interest rates based on trust scores

### 2. NFT Marketplaces
- Identify influential creators and collectors
- Prevent fake account creation
- Build reputation systems for traders

### 3. Airdrop Distribution
- Target high-quality social media accounts
- Prevent Sybil attacks through verification
- Reward genuine community members

### 4. Social Trading Platforms
- Rank traders by social influence and activity
- Verify trader identities across platforms
- Build trust networks for copy trading

## Security Considerations

1. **Privacy Protection**: User data is processed locally
2. **Address Validation**: All wallet addresses are format-validated
3. **Verification Levels**: Multiple verification tiers prevent fraud
4. **Trust Scoring**: Combines multiple factors to assess reliability

## Future Enhancements

1. **Real-time Updates**: Live social media monitoring
2. **Cross-Platform Integration**: Support for additional social networks
3. **Machine Learning**: Advanced fraud detection algorithms
4. **Blockchain Integration**: On-chain verification mechanisms
5. **API Development**: RESTful API for external integrations

## Requirements

```
pandas>=1.3.0
numpy>=1.21.0
python>=3.8
```

## Installation

```bash
pip install pandas numpy
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
