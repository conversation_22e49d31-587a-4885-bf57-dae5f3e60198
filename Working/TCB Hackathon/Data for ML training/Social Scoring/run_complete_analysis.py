#!/usr/bin/env python3
"""
Complete Social Scoring and Wallet Linking Analysis
Runs the full pipeline and generates comprehensive analytics
"""

import pandas as pd
import numpy as np
import json
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from social_scoring_calculator import SocialScoringCalculator
from wallet_linking_system import WalletLinkingSystem

class SocialScoringAnalytics:
    """
    Comprehensive analytics for social scoring and wallet linking system
    """
    
    def __init__(self):
        self.calculator = SocialScoringCalculator()
        self.linking_system = WalletLinkingSystem()
        
    def run_complete_analysis(self, input_file: str = 'twitter_users_social_scoring_50K.csv'):
        """Run the complete analysis pipeline"""
        
        print("=" * 80)
        print("SOCIAL SCORING AND WALLET LINKING ANALYSIS")
        print("=" * 80)
        
        # Step 1: Load and validate data
        print("\n1. Loading and validating data...")
        try:
            df = pd.read_csv(input_file)
            print(f"✓ Loaded {len(df)} users from {input_file}")
        except FileNotFoundError:
            print(f"✗ Error: {input_file} not found")
            return None
        
        # Step 2: Calculate social scores
        print("\n2. Calculating social scores...")
        scored_df = self.calculator.process_dataset(df)
        scored_df.to_csv('twitter_users_with_social_scores.csv', index=False)
        print("✓ Social scores calculated and saved")
        
        # Step 3: Process wallet linking
        print("\n3. Processing wallet linking...")
        wallet_df = self.linking_system.process_social_data_with_wallets(
            scored_df, generate_mock_wallets=True
        )
        wallet_df.to_csv('social_wallet_links.csv', index=False)
        print("✓ Wallet links created and saved")
        
        # Step 4: Create combined dataset
        print("\n4. Creating combined dataset...")
        combined_df = self.create_combined_dataset(scored_df, wallet_df)
        combined_df.to_csv('social_scores_with_wallets.csv', index=False)
        print("✓ Combined dataset created and saved")
        
        # Step 5: Generate analytics
        print("\n5. Generating comprehensive analytics...")
        analytics = self.generate_analytics(scored_df, wallet_df, combined_df)
        
        # Save analytics to JSON
        with open('social_scoring_analytics.json', 'w') as f:
            json.dump(analytics, f, indent=2, default=str)
        print("✓ Analytics saved to social_scoring_analytics.json")
        
        # Step 6: Generate visualizations
        print("\n6. Creating visualizations...")
        self.create_visualizations(scored_df, wallet_df, combined_df)
        print("✓ Visualizations saved")
        
        # Step 7: Display summary
        print("\n7. Analysis Summary:")
        self.display_summary(analytics)
        
        return analytics
    
    def create_combined_dataset(self, scored_df: pd.DataFrame, wallet_df: pd.DataFrame) -> pd.DataFrame:
        """Create combined dataset with social scores and wallet information"""
        
        # Aggregate wallet data by user
        wallet_summary = wallet_df.groupby('user_id').agg({
            'wallet_address': 'count',
            'network': lambda x: ','.join(x.unique()),
            'trust_score': ['mean', 'max'],
            'verification_level': lambda x: ','.join(x.unique()),
            'verification_score': 'max'
        }).round(2)
        
        # Flatten column names
        wallet_summary.columns = [
            'wallet_count', 'networks', 'avg_trust_score', 'max_trust_score',
            'verification_levels', 'max_verification_score'
        ]
        wallet_summary = wallet_summary.reset_index()
        
        # Merge with social data
        combined_df = scored_df.merge(
            wallet_summary, 
            left_on='_id', 
            right_on='user_id', 
            how='left'
        )
        
        # Fill missing values
        combined_df['wallet_count'] = combined_df['wallet_count'].fillna(0)
        combined_df['avg_trust_score'] = combined_df['avg_trust_score'].fillna(0)
        combined_df['max_trust_score'] = combined_df['max_trust_score'].fillna(0)
        combined_df['max_verification_score'] = combined_df['max_verification_score'].fillna(0)
        
        # Drop duplicate user_id column
        combined_df = combined_df.drop('user_id', axis=1)
        
        return combined_df
    
    def generate_analytics(self, scored_df: pd.DataFrame, wallet_df: pd.DataFrame, 
                          combined_df: pd.DataFrame) -> dict:
        """Generate comprehensive analytics"""
        
        analytics = {
            'analysis_timestamp': datetime.now().isoformat(),
            'dataset_info': {
                'total_users': len(scored_df),
                'users_with_wallets': len(wallet_df['user_id'].unique()),
                'total_wallet_links': len(wallet_df),
                'coverage_percentage': round(len(wallet_df['user_id'].unique()) / len(scored_df) * 100, 2)
            },
            'social_score_analytics': self.analyze_social_scores(scored_df),
            'wallet_analytics': self.analyze_wallets(wallet_df),
            'trust_score_analytics': self.analyze_trust_scores(wallet_df),
            'correlation_analysis': self.analyze_correlations(combined_df),
            'risk_assessment': self.assess_risks(combined_df),
            'recommendations': self.generate_recommendations(combined_df)
        }
        
        return analytics
    
    def analyze_social_scores(self, df: pd.DataFrame) -> dict:
        """Analyze social score distribution and patterns"""
        
        return {
            'score_distribution': {
                'mean': round(df['social_score'].mean(), 2),
                'median': round(df['social_score'].median(), 2),
                'std': round(df['social_score'].std(), 2),
                'min': round(df['social_score'].min(), 2),
                'max': round(df['social_score'].max(), 2)
            },
            'tier_distribution': df['score_tier'].value_counts().to_dict(),
            'component_scores': {
                'engagement': {
                    'mean': round(df['engagement_score'].mean(), 2),
                    'correlation_with_total': round(df['engagement_score'].corr(df['social_score']), 3)
                },
                'influence': {
                    'mean': round(df['influence_score'].mean(), 2),
                    'correlation_with_total': round(df['influence_score'].corr(df['social_score']), 3)
                },
                'quality': {
                    'mean': round(df['quality_score'].mean(), 2),
                    'correlation_with_total': round(df['quality_score'].corr(df['social_score']), 3)
                },
                'activity': {
                    'mean': round(df['activity_score'].mean(), 2),
                    'correlation_with_total': round(df['activity_score'].corr(df['social_score']), 3)
                }
            },
            'high_performers': {
                'count': len(df[df['social_score'] >= 80]),
                'percentage': round(len(df[df['social_score'] >= 80]) / len(df) * 100, 2)
            }
        }
    
    def analyze_wallets(self, wallet_df: pd.DataFrame) -> dict:
        """Analyze wallet distribution and patterns"""
        
        return {
            'network_distribution': wallet_df['network'].value_counts().to_dict(),
            'verification_distribution': wallet_df['verification_level'].value_counts().to_dict(),
            'wallets_per_user': {
                'mean': round(wallet_df.groupby('user_id').size().mean(), 2),
                'max': wallet_df.groupby('user_id').size().max(),
                'users_with_multiple_wallets': len(wallet_df.groupby('user_id').size()[wallet_df.groupby('user_id').size() > 1])
            },
            'popular_networks': wallet_df['network'].value_counts().head(5).to_dict()
        }
    
    def analyze_trust_scores(self, wallet_df: pd.DataFrame) -> dict:
        """Analyze trust score patterns"""
        
        return {
            'trust_score_distribution': {
                'mean': round(wallet_df['trust_score'].mean(), 2),
                'median': round(wallet_df['trust_score'].median(), 2),
                'std': round(wallet_df['trust_score'].std(), 2)
            },
            'high_trust_accounts': {
                'count': len(wallet_df[wallet_df['trust_score'] >= 70]),
                'percentage': round(len(wallet_df[wallet_df['trust_score'] >= 70]) / len(wallet_df) * 100, 2)
            },
            'trust_by_verification': wallet_df.groupby('verification_level')['trust_score'].mean().round(2).to_dict(),
            'trust_by_network': wallet_df.groupby('network')['trust_score'].mean().round(2).to_dict()
        }
    
    def analyze_correlations(self, combined_df: pd.DataFrame) -> dict:
        """Analyze correlations between different metrics"""
        
        numeric_cols = ['social_score', 'engagement_score', 'influence_score', 
                       'quality_score', 'activity_score', 'followersCount', 
                       'wallet_count', 'avg_trust_score']
        
        # Filter to only include columns that exist
        available_cols = [col for col in numeric_cols if col in combined_df.columns]
        corr_matrix = combined_df[available_cols].corr()
        
        return {
            'social_score_correlations': {
                col: round(corr_matrix.loc['social_score', col], 3) 
                for col in available_cols if col != 'social_score'
            },
            'wallet_count_correlation': round(corr_matrix.loc['social_score', 'wallet_count'], 3) if 'wallet_count' in available_cols else None,
            'trust_score_correlation': round(corr_matrix.loc['social_score', 'avg_trust_score'], 3) if 'avg_trust_score' in available_cols else None
        }
    
    def assess_risks(self, combined_df: pd.DataFrame) -> dict:
        """Assess potential risks and fraud indicators"""
        
        # Define risk indicators
        high_risk_users = combined_df[
            (combined_df['social_score'] < 30) | 
            (combined_df['wallet_count'] > 5) |
            (combined_df['avg_trust_score'] < 20)
        ]
        
        return {
            'high_risk_users': {
                'count': len(high_risk_users),
                'percentage': round(len(high_risk_users) / len(combined_df) * 100, 2)
            },
            'low_social_high_wallets': len(combined_df[
                (combined_df['social_score'] < 40) & (combined_df['wallet_count'] > 3)
            ]),
            'unverified_high_activity': len(combined_df[
                (combined_df['max_verification_score'] == 0) & (combined_df['social_score'] > 70)
            ]) if 'max_verification_score' in combined_df.columns else 0
        }
    
    def generate_recommendations(self, combined_df: pd.DataFrame) -> list:
        """Generate actionable recommendations"""
        
        recommendations = []
        
        # High-value users for partnerships
        high_value = len(combined_df[
            (combined_df['social_score'] >= 70) & (combined_df['wallet_count'] >= 1)
        ])
        recommendations.append(f"Target {high_value} high-value users for premium features or partnerships")
        
        # Users needing verification
        unverified = len(combined_df[combined_df['wallet_count'] == 0])
        recommendations.append(f"Encourage {unverified} users to link wallet addresses for better trust scores")
        
        # Quality improvement opportunities
        low_quality = len(combined_df[combined_df['social_score'] < 50])
        recommendations.append(f"Provide engagement tips to {low_quality} users with below-average social scores")
        
        return recommendations
    
    def create_visualizations(self, scored_df: pd.DataFrame, wallet_df: pd.DataFrame, 
                            combined_df: pd.DataFrame):
        """Create and save visualizations"""
        
        plt.style.use('default')
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Social Scoring and Wallet Linking Analytics', fontsize=16, fontweight='bold')
        
        # 1. Social Score Distribution
        axes[0, 0].hist(scored_df['social_score'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('Social Score Distribution')
        axes[0, 0].set_xlabel('Social Score')
        axes[0, 0].set_ylabel('Frequency')
        
        # 2. Score Tier Distribution
        tier_counts = scored_df['score_tier'].value_counts()
        axes[0, 1].pie(tier_counts.values, labels=tier_counts.index, autopct='%1.1f%%')
        axes[0, 1].set_title('Score Tier Distribution')
        
        # 3. Network Distribution
        network_counts = wallet_df['network'].value_counts()
        axes[0, 2].bar(network_counts.index, network_counts.values, color='lightgreen')
        axes[0, 2].set_title('Wallet Network Distribution')
        axes[0, 2].tick_params(axis='x', rotation=45)
        
        # 4. Trust Score vs Social Score
        axes[1, 0].scatter(combined_df['social_score'], combined_df['avg_trust_score'], 
                          alpha=0.6, color='orange')
        axes[1, 0].set_title('Trust Score vs Social Score')
        axes[1, 0].set_xlabel('Social Score')
        axes[1, 0].set_ylabel('Average Trust Score')
        
        # 5. Verification Level Distribution
        verification_counts = wallet_df['verification_level'].value_counts()
        axes[1, 1].bar(verification_counts.index, verification_counts.values, color='lightcoral')
        axes[1, 1].set_title('Verification Level Distribution')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        # 6. Component Scores Comparison
        component_means = [
            scored_df['engagement_score'].mean(),
            scored_df['influence_score'].mean(),
            scored_df['quality_score'].mean(),
            scored_df['activity_score'].mean()
        ]
        component_names = ['Engagement', 'Influence', 'Quality', 'Activity']
        axes[1, 2].bar(component_names, component_means, color='lightblue')
        axes[1, 2].set_title('Average Component Scores')
        axes[1, 2].set_ylabel('Score')
        axes[1, 2].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig('social_scoring_analytics.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def display_summary(self, analytics: dict):
        """Display analysis summary"""
        
        print("\n" + "=" * 60)
        print("ANALYSIS SUMMARY")
        print("=" * 60)
        
        dataset_info = analytics['dataset_info']
        print(f"📊 Total Users Analyzed: {dataset_info['total_users']:,}")
        print(f"🔗 Users with Wallets: {dataset_info['users_with_wallets']:,}")
        print(f"💰 Total Wallet Links: {dataset_info['total_wallet_links']:,}")
        print(f"📈 Coverage: {dataset_info['coverage_percentage']}%")
        
        social_analytics = analytics['social_score_analytics']
        print(f"\n🎯 Average Social Score: {social_analytics['score_distribution']['mean']}")
        print(f"⭐ High Performers (80+): {social_analytics['high_performers']['count']} ({social_analytics['high_performers']['percentage']}%)")
        
        trust_analytics = analytics['trust_score_analytics']
        print(f"🛡️ Average Trust Score: {trust_analytics['trust_score_distribution']['mean']}")
        print(f"🔒 High Trust Accounts (70+): {trust_analytics['high_trust_accounts']['count']} ({trust_analytics['high_trust_accounts']['percentage']}%)")
        
        print(f"\n📋 Key Recommendations:")
        for i, rec in enumerate(analytics['recommendations'], 1):
            print(f"   {i}. {rec}")
        
        print(f"\n📁 Output Files Generated:")
        print(f"   • twitter_users_with_social_scores.csv")
        print(f"   • social_wallet_links.csv")
        print(f"   • social_scores_with_wallets.csv")
        print(f"   • social_scoring_analytics.json")
        print(f"   • social_scoring_analytics.png")

def main():
    """Run the complete analysis"""
    analytics_system = SocialScoringAnalytics()
    analytics_system.run_complete_analysis()

if __name__ == "__main__":
    main()
