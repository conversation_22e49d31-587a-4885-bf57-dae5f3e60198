#!/usr/bin/env python3
"""
SHAP Analysis for Social Scoring System
Provides explainable AI insights into scoring decisions
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import shap
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_squared_error, r2_score
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

class SocialScoringExplainer:
    """
    SHAP-based explainer for social scoring decisions
    Provides feature importance and individual prediction explanations
    """
    
    def __init__(self):
        self.model = None
        self.explainer = None
        self.scaler = StandardScaler()
        self.feature_names = []
        self.shap_values = None
        self.X_test = None
        self.y_test = None
        
    def prepare_features(self, df):
        """Prepare features for ML model training"""
        
        # Select numerical features for modeling
        feature_cols = [
            'followersCount', 'friendsCount', 'statusesCount', 'favouritesCount',
            'listedCount', 'mediaCount', 'verified', 'blue',
            'engagement_score', 'influence_score', 'quality_score', 'activity_score'
        ]
        
        # Create additional engineered features
        df_features = df.copy()
        
        # Ratios and derived features
        df_features['follower_following_ratio'] = np.log1p(df_features['followersCount']) / np.log1p(df_features['friendsCount'] + 1)
        df_features['engagement_rate'] = df_features['favouritesCount'] / (df_features['statusesCount'] + 1)
        df_features['media_per_tweet'] = df_features['mediaCount'] / (df_features['statusesCount'] + 1)
        df_features['listed_per_follower'] = df_features['listedCount'] / (df_features['followersCount'] + 1)
        
        # Log transformations for skewed features
        df_features['log_followers'] = np.log1p(df_features['followersCount'])
        df_features['log_following'] = np.log1p(df_features['friendsCount'])
        df_features['log_tweets'] = np.log1p(df_features['statusesCount'])
        df_features['log_likes'] = np.log1p(df_features['favouritesCount'])
        
        # Account age (if created date is available)
        if 'created' in df_features.columns:
            df_features['created'] = pd.to_datetime(df_features['created'], errors='coerce', utc=True)
            now = pd.Timestamp.now(tz='UTC')
            df_features['account_age_days'] = (now - df_features['created']).dt.days
            df_features['account_age_years'] = df_features['account_age_days'] / 365.25
            # Fill NaN values with median
            df_features['account_age_years'] = df_features['account_age_years'].fillna(df_features['account_age_years'].median())
        else:
            df_features['account_age_years'] = 1.0  # Default value
        
        # Binary features (handle NaN values first)
        df_features['verified'] = df_features['verified'].fillna(False).astype(int)
        df_features['blue'] = df_features['blue'].fillna(False).astype(int)
        df_features['has_bio'] = (~df_features['rawDescription'].isna() &
                                 (df_features['rawDescription'].str.len() > 10)).astype(int)
        
        # Activity level categories
        df_features['activity_level'] = pd.cut(
            df_features['statusesCount'],
            bins=[0, 100, 1000, 10000, float('inf')],
            labels=[0, 1, 2, 3]
        )
        df_features['activity_level'] = df_features['activity_level'].fillna(0).astype(int)

        # Influence level categories
        df_features['influence_level'] = pd.cut(
            df_features['followersCount'],
            bins=[0, 100, 1000, 10000, 100000, float('inf')],
            labels=[0, 1, 2, 3, 4]
        )
        df_features['influence_level'] = df_features['influence_level'].fillna(0).astype(int)
        
        # Final feature list
        self.feature_names = [
            'log_followers', 'log_following', 'log_tweets', 'log_likes',
            'listedCount', 'mediaCount', 'verified', 'blue', 'has_bio',
            'follower_following_ratio', 'engagement_rate', 'media_per_tweet', 
            'listed_per_follower', 'account_age_years', 'activity_level', 'influence_level',
            'engagement_score', 'influence_score', 'quality_score', 'activity_score'
        ]
        
        # Handle missing values
        for col in self.feature_names:
            if col in df_features.columns:
                df_features[col] = df_features[col].fillna(df_features[col].median())
            else:
                df_features[col] = 0
        
        return df_features[self.feature_names]
    
    def train_model(self, df, target_col='social_score'):
        """Train ML model for SHAP analysis"""
        
        print("Preparing features for ML model...")
        X = self.prepare_features(df)
        y = df[target_col]
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Train ensemble model
        print("Training Random Forest model...")
        self.model = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            n_jobs=-1
        )
        
        self.model.fit(X_train_scaled, y_train)
        
        # Evaluate model
        y_pred = self.model.predict(X_test_scaled)
        mse = mean_squared_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        print(f"Model Performance:")
        print(f"  R² Score: {r2:.3f}")
        print(f"  RMSE: {np.sqrt(mse):.3f}")
        
        # Store test data for SHAP analysis
        self.X_test = X_test_scaled
        self.y_test = y_test
        
        return X_train_scaled, X_test_scaled, y_train, y_test
    
    def create_shap_explainer(self, X_train):
        """Create SHAP explainer"""
        
        print("Creating SHAP explainer...")
        
        # Use TreeExplainer for tree-based models
        self.explainer = shap.TreeExplainer(self.model)
        
        # Calculate SHAP values for test set
        print("Calculating SHAP values...")
        self.shap_values = self.explainer.shap_values(self.X_test)
        
        print("SHAP analysis complete!")
        
    def plot_feature_importance(self, save_path=None):
        """Plot feature importance using SHAP"""
        
        if self.shap_values is None:
            print("Please run create_shap_explainer first!")
            return
        
        plt.figure(figsize=(12, 8))
        
        # Summary plot
        shap.summary_plot(
            self.shap_values, 
            self.X_test, 
            feature_names=self.feature_names,
            show=False
        )
        
        plt.title("SHAP Feature Importance Summary", fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_feature_importance_bar(self, save_path=None):
        """Plot feature importance as bar chart"""
        
        if self.shap_values is None:
            print("Please run create_shap_explainer first!")
            return
        
        # Calculate mean absolute SHAP values
        feature_importance = np.abs(self.shap_values).mean(0)
        
        # Create DataFrame for plotting
        importance_df = pd.DataFrame({
            'feature': self.feature_names,
            'importance': feature_importance
        }).sort_values('importance', ascending=True)
        
        plt.figure(figsize=(10, 8))
        plt.barh(importance_df['feature'], importance_df['importance'])
        plt.xlabel('Mean |SHAP Value|')
        plt.title('Feature Importance (SHAP)', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
        
        return importance_df
    
    def explain_individual_prediction(self, user_index=0, save_path=None):
        """Explain individual user's score prediction"""
        
        if self.shap_values is None:
            print("Please run create_shap_explainer first!")
            return
        
        plt.figure(figsize=(12, 6))
        
        # Waterfall plot for individual prediction
        shap.waterfall_plot(
            shap.Explanation(
                values=self.shap_values[user_index],
                base_values=self.explainer.expected_value,
                data=self.X_test[user_index],
                feature_names=self.feature_names
            ),
            show=False
        )
        
        plt.title(f"SHAP Explanation for User {user_index}", fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def plot_partial_dependence(self, feature_name, save_path=None):
        """Plot partial dependence for a specific feature"""
        
        if self.shap_values is None:
            print("Please run create_shap_explainer first!")
            return
        
        if feature_name not in self.feature_names:
            print(f"Feature {feature_name} not found!")
            return
        
        feature_idx = self.feature_names.index(feature_name)
        
        plt.figure(figsize=(10, 6))
        
        # Partial dependence plot
        shap.plots.partial_dependence(
            feature_name, self.model.predict, self.X_test, ice=False,
            model_expected_value=True, feature_expected_value=True,
            show=False
        )
        
        plt.title(f'Partial Dependence: {feature_name}', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def create_interactive_shap_plots(self):
        """Create interactive SHAP plots using Plotly"""
        
        if self.shap_values is None:
            print("Please run create_shap_explainer first!")
            return
        
        # Feature importance
        feature_importance = np.abs(self.shap_values).mean(0)
        importance_df = pd.DataFrame({
            'feature': self.feature_names,
            'importance': feature_importance
        }).sort_values('importance', ascending=False)
        
        # Interactive bar chart
        fig_bar = px.bar(
            importance_df.head(15),
            x='importance',
            y='feature',
            orientation='h',
            title='Top 15 Feature Importance (SHAP)',
            labels={'importance': 'Mean |SHAP Value|', 'feature': 'Feature'}
        )
        fig_bar.update_layout(height=600)
        
        # SHAP values scatter plot
        fig_scatter = make_subplots(
            rows=2, cols=2,
            subplot_titles=['Engagement Score', 'Influence Score', 'Quality Score', 'Activity Score']
        )
        
        key_features = ['engagement_score', 'influence_score', 'quality_score', 'activity_score']
        
        for i, feature in enumerate(key_features):
            if feature in self.feature_names:
                feature_idx = self.feature_names.index(feature)
                row = (i // 2) + 1
                col = (i % 2) + 1
                
                fig_scatter.add_trace(
                    go.Scatter(
                        x=self.X_test[:, feature_idx],
                        y=self.shap_values[:, feature_idx],
                        mode='markers',
                        name=feature,
                        opacity=0.6
                    ),
                    row=row, col=col
                )
        
        fig_scatter.update_layout(
            title='SHAP Values vs Feature Values',
            height=600,
            showlegend=False
        )
        
        return fig_bar, fig_scatter
    
    def generate_insights_report(self):
        """Generate insights report from SHAP analysis"""
        
        if self.shap_values is None:
            print("Please run create_shap_explainer first!")
            return
        
        # Feature importance analysis
        feature_importance = np.abs(self.shap_values).mean(0)
        importance_df = pd.DataFrame({
            'feature': self.feature_names,
            'importance': feature_importance
        }).sort_values('importance', ascending=False)
        
        # Generate insights
        insights = {
            'model_performance': {
                'r2_score': r2_score(self.y_test, self.model.predict(self.X_test)),
                'rmse': np.sqrt(mean_squared_error(self.y_test, self.model.predict(self.X_test)))
            },
            'top_features': importance_df.head(10).to_dict('records'),
            'feature_insights': {},
            'scoring_patterns': {}
        }
        
        # Analyze top features
        for _, row in importance_df.head(5).iterrows():
            feature = row['feature']
            feature_idx = self.feature_names.index(feature)
            
            # Calculate correlation with SHAP values
            correlation = np.corrcoef(
                self.X_test[:, feature_idx], 
                self.shap_values[:, feature_idx]
            )[0, 1]
            
            insights['feature_insights'][feature] = {
                'importance': row['importance'],
                'correlation': correlation,
                'impact': 'positive' if correlation > 0 else 'negative'
            }
        
        # Analyze scoring patterns
        high_scores = self.y_test >= 80
        low_scores = self.y_test <= 30
        
        if np.sum(high_scores) > 0 and np.sum(low_scores) > 0:
            high_score_shap = self.shap_values[high_scores].mean(0)
            low_score_shap = self.shap_values[low_scores].mean(0)
            
            # Find features that differentiate high vs low scores
            shap_diff = high_score_shap - low_score_shap
            
            insights['scoring_patterns'] = {
                'high_score_drivers': [
                    {'feature': self.feature_names[i], 'shap_contribution': shap_diff[i]}
                    for i in np.argsort(shap_diff)[-5:][::-1]
                ],
                'low_score_indicators': [
                    {'feature': self.feature_names[i], 'shap_contribution': shap_diff[i]}
                    for i in np.argsort(shap_diff)[:5]
                ]
            }
        
        return insights

def main():
    """Main function to run SHAP analysis"""
    
    print("=" * 80)
    print("SHAP ANALYSIS FOR SOCIAL SCORING SYSTEM")
    print("=" * 80)
    
    # Load data
    print("\nLoading social scoring data...")
    try:
        df = pd.read_csv('twitter_users_with_social_scores.csv')
        print(f"✓ Loaded {len(df)} users")
    except FileNotFoundError:
        print("✗ Error: twitter_users_with_social_scores.csv not found")
        print("Please run social_scoring_calculator.py first.")
        return
    
    # Initialize explainer
    explainer = SocialScoringExplainer()
    
    # Train model
    X_train, X_test, y_train, y_test = explainer.train_model(df)
    
    # Create SHAP explainer
    explainer.create_shap_explainer(X_train)
    
    # Generate visualizations
    print("\nGenerating SHAP visualizations...")
    
    # Feature importance plots
    explainer.plot_feature_importance_bar('shap_feature_importance.png')
    explainer.plot_feature_importance('shap_summary_plot.png')
    
    # Individual explanations
    print("\nGenerating individual explanations...")
    for i in [0, 10, 50]:  # Explain a few different users
        explainer.explain_individual_prediction(i, f'shap_individual_{i}.png')
    
    # Generate insights report
    print("\nGenerating insights report...")
    insights = explainer.generate_insights_report()
    
    # Save insights to JSON
    import json
    with open('shap_insights_report.json', 'w') as f:
        json.dump(insights, f, indent=2, default=str)
    
    print("✓ SHAP analysis complete!")
    print("\nGenerated files:")
    print("  • shap_feature_importance.png")
    print("  • shap_summary_plot.png")
    print("  • shap_individual_*.png")
    print("  • shap_insights_report.json")
    
    # Display key insights
    print(f"\n📊 Key Insights:")
    print(f"  • Model R² Score: {insights['model_performance']['r2_score']:.3f}")
    print(f"  • Model RMSE: {insights['model_performance']['rmse']:.3f}")
    print(f"  • Top Feature: {insights['top_features'][0]['feature']}")
    print(f"  • Feature Importance: {insights['top_features'][0]['importance']:.3f}")

if __name__ == "__main__":
    main()
