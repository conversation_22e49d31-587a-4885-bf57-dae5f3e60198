#!/usr/bin/env python3
"""
Demo script to showcase social scoring and wallet linking results
"""

import pandas as pd
import numpy as np

def main():
    print("=" * 80)
    print("SOCIAL SCORING AND WALLET LINKING DEMO")
    print("=" * 80)
    
    # Load the results
    print("\n📊 Loading Results...")
    social_df = pd.read_csv('twitter_users_with_social_scores.csv')
    wallet_df = pd.read_csv('social_wallet_links.csv')
    combined_df = pd.read_csv('social_scores_with_wallets.csv')
    
    print(f"✓ Social scores: {len(social_df):,} users")
    print(f"✓ Wallet links: {len(wallet_df):,} links")
    print(f"✓ Combined data: {len(combined_df):,} users")
    
    # Show sample social scores
    print("\n🎯 Sample Social Scores:")
    print("-" * 60)
    sample_cols = ['_id', 'followersCount', 'social_score', 'score_tier']
    sample_data = social_df[sample_cols].head(10)
    for _, row in sample_data.iterrows():
        print(f"User {row['_id']}: {row['social_score']:.1f} ({row['score_tier']}) - {row['followersCount']:,} followers")
    
    # Show top performers
    print("\n⭐ Top 10 Social Performers:")
    print("-" * 60)
    top_performers = social_df.nlargest(10, 'social_score')[['_id', 'followersCount', 'social_score', 'score_tier']]
    for _, row in top_performers.iterrows():
        print(f"User {row['_id']}: {row['social_score']:.1f} ({row['score_tier']}) - {row['followersCount']:,} followers")
    
    # Show wallet linking examples
    print("\n🔗 Sample Wallet Links:")
    print("-" * 60)
    sample_wallets = wallet_df.head(10)
    for _, row in sample_wallets.iterrows():
        print(f"User {row['user_id']}: {row['network']} - {row['wallet_address'][:20]}... (Trust: {row['trust_score']:.1f})")
    
    # Show high trust wallet links
    print("\n🛡️ High Trust Wallet Links:")
    print("-" * 60)
    high_trust = wallet_df.nlargest(10, 'trust_score')
    for _, row in high_trust.iterrows():
        print(f"User {row['user_id']}: {row['network']} - Trust: {row['trust_score']:.1f} ({row['verification_level']})")
    
    # Statistics
    print("\n📈 Key Statistics:")
    print("-" * 60)
    print(f"Average Social Score: {social_df['social_score'].mean():.2f}")
    print(f"Median Social Score: {social_df['social_score'].median():.2f}")
    print(f"Users with Excellent scores (80+): {len(social_df[social_df['social_score'] >= 80]):,}")
    print(f"Users with Good+ scores (65+): {len(social_df[social_df['social_score'] >= 65]):,}")
    
    print(f"\nAverage Trust Score: {wallet_df['trust_score'].mean():.2f}")
    print(f"High Trust Links (70+): {len(wallet_df[wallet_df['trust_score'] >= 70]):,}")
    print(f"Users with Multiple Wallets: {len(wallet_df.groupby('user_id').size()[wallet_df.groupby('user_id').size() > 1]):,}")
    
    # Network distribution
    print(f"\n🌐 Network Distribution:")
    print("-" * 60)
    network_dist = wallet_df['network'].value_counts()
    for network, count in network_dist.items():
        percentage = (count / len(wallet_df)) * 100
        print(f"{network.capitalize()}: {count:,} ({percentage:.1f}%)")
    
    # Verification levels
    print(f"\n🔐 Verification Levels:")
    print("-" * 60)
    verification_dist = wallet_df['verification_level'].value_counts()
    for level, count in verification_dist.items():
        percentage = (count / len(wallet_df)) * 100
        print(f"{level.replace('_', ' ').title()}: {count:,} ({percentage:.1f}%)")
    
    # Score tier distribution
    print(f"\n🏆 Score Tier Distribution:")
    print("-" * 60)
    tier_dist = social_df['score_tier'].value_counts()
    for tier, count in tier_dist.items():
        percentage = (count / len(social_df)) * 100
        print(f"{tier}: {count:,} ({percentage:.1f}%)")
    
    # Use case examples
    print("\n💡 Use Case Examples:")
    print("-" * 60)
    
    # High-value users for DeFi lending
    high_value_defi = combined_df[
        (combined_df['social_score'] >= 70) & 
        (combined_df['wallet_count'] >= 1) &
        (combined_df['avg_trust_score'] >= 50)
    ]
    print(f"DeFi Lending Candidates (High social + verified wallets): {len(high_value_defi):,}")
    
    # NFT marketplace influencers
    nft_influencers = combined_df[
        (combined_df['social_score'] >= 65) & 
        (combined_df['followersCount'] >= 1000)
    ]
    print(f"NFT Marketplace Influencers (Good scores + followers): {len(nft_influencers):,}")
    
    # Airdrop targets (using available columns)
    airdrop_targets = combined_df[
        (combined_df['social_score'] >= 50) &
        (combined_df['wallet_count'] >= 1) &
        (combined_df['verification_levels'].str.contains('social_verified|on_chain_verified', na=False))
    ]
    print(f"Quality Airdrop Targets (Verified users): {len(airdrop_targets):,}")

    # Risk assessment
    potential_risks = combined_df[
        (combined_df['social_score'] < 30) |
        (combined_df['wallet_count'] > 5)
    ]
    print(f"Potential Risk Accounts (Low scores or many wallets): {len(potential_risks):,}")
    
    print("\n" + "=" * 80)
    print("DEMO COMPLETE - Check the CSV files for full data!")
    print("=" * 80)

if __name__ == "__main__":
    main()
