#!/usr/bin/env python3
"""
Wallet Address Linking System for Social Scoring
Links Twitter social scores to cryptocurrency wallet addresses
"""

import pandas as pd
import numpy as np
import hashlib
import re
import json
from typing import Dict, List, Optional, Tuple
import random
import string

class WalletLinkingSystem:
    """
    System to link social media profiles to cryptocurrency wallet addresses
    Supports multiple blockchain networks and provides verification methods
    """
    
    def __init__(self):
        self.supported_networks = {
            'ethereum': {'prefix': '0x', 'length': 42},
            'bitcoin': {'prefix': ['1', '3', 'bc1'], 'length': [26, 35, 42, 62]},
            'solana': {'prefix': '', 'length': 44},
            'polygon': {'prefix': '0x', 'length': 42},
            'binance': {'prefix': '0x', 'length': 42},
            'cardano': {'prefix': 'addr1', 'length': 103},
            'avalanche': {'prefix': '0x', 'length': 42}
        }
        
        # Verification status levels
        self.verification_levels = {
            'unverified': 0,
            'self_claimed': 1,
            'social_verified': 2,
            'on_chain_verified': 3,
            'kyc_verified': 4
        }
    
    def generate_mock_wallet_address(self, network: str = 'ethereum') -> str:
        """Generate a mock wallet address for demonstration purposes"""
        if network not in self.supported_networks:
            raise ValueError(f"Unsupported network: {network}")
        
        config = self.supported_networks[network]
        
        if network == 'ethereum' or network == 'polygon' or network == 'binance' or network == 'avalanche':
            # Generate Ethereum-style address
            return '0x' + ''.join(random.choices('0123456789abcdef', k=40))
        
        elif network == 'bitcoin':
            # Generate Bitcoin-style address (simplified)
            prefix = random.choice(['1', '3'])
            length = random.choice([26, 35]) - len(prefix)
            chars = string.ascii_letters + string.digits
            return prefix + ''.join(random.choices(chars, k=length))
        
        elif network == 'solana':
            # Generate Solana-style address
            chars = string.ascii_letters + string.digits
            return ''.join(random.choices(chars, k=44))
        
        elif network == 'cardano':
            # Generate Cardano-style address
            return 'addr1' + ''.join(random.choices('0123456789abcdef', k=99))
    
    def validate_wallet_address(self, address: str, network: str) -> bool:
        """Validate wallet address format for given network"""
        if not address or network not in self.supported_networks:
            return False
        
        config = self.supported_networks[network]
        
        # Check prefix
        if isinstance(config['prefix'], str):
            if not address.startswith(config['prefix']):
                return False
        elif isinstance(config['prefix'], list):
            if not any(address.startswith(p) for p in config['prefix']):
                return False
        
        # Check length
        if isinstance(config['length'], int):
            if len(address) != config['length']:
                return False
        elif isinstance(config['length'], list):
            if len(address) not in config['length']:
                return False
        
        return True
    
    def extract_wallet_from_bio(self, bio: str) -> List[Dict[str, str]]:
        """Extract potential wallet addresses from user bio"""
        if not bio or pd.isna(bio):
            return []
        
        found_wallets = []
        
        # Ethereum-style addresses (0x followed by 40 hex chars)
        eth_pattern = r'0x[a-fA-F0-9]{40}'
        eth_matches = re.findall(eth_pattern, bio)
        for match in eth_matches:
            found_wallets.append({
                'address': match,
                'network': 'ethereum',
                'confidence': 0.9,
                'source': 'bio_extraction'
            })
        
        # Bitcoin addresses (simplified patterns)
        btc_patterns = [
            r'\b[13][a-km-zA-HJ-NP-Z1-9]{25,34}\b',  # Legacy addresses
            r'\bbc1[a-z0-9]{39,59}\b'  # Bech32 addresses
        ]
        for pattern in btc_patterns:
            btc_matches = re.findall(pattern, bio)
            for match in btc_matches:
                found_wallets.append({
                    'address': match,
                    'network': 'bitcoin',
                    'confidence': 0.8,
                    'source': 'bio_extraction'
                })
        
        # Solana addresses (base58, ~44 chars)
        sol_pattern = r'\b[1-9A-HJ-NP-Za-km-z]{32,44}\b'
        sol_matches = re.findall(sol_pattern, bio)
        for match in sol_matches:
            # Additional validation for Solana
            if len(match) >= 32 and not match.startswith('0x'):
                found_wallets.append({
                    'address': match,
                    'network': 'solana',
                    'confidence': 0.7,
                    'source': 'bio_extraction'
                })
        
        return found_wallets
    
    def create_social_wallet_link(self, user_id: str, social_score: float, 
                                 wallet_address: str, network: str,
                                 verification_level: str = 'self_claimed') -> Dict:
        """Create a link between social profile and wallet address"""
        
        # Validate inputs
        if not self.validate_wallet_address(wallet_address, network):
            raise ValueError(f"Invalid wallet address for network {network}")
        
        if verification_level not in self.verification_levels:
            raise ValueError(f"Invalid verification level: {verification_level}")
        
        # Create unique link ID
        link_data = f"{user_id}_{wallet_address}_{network}"
        link_id = hashlib.sha256(link_data.encode()).hexdigest()[:16]
        
        # Calculate trust score based on social score and verification
        trust_multiplier = {
            'unverified': 0.3,
            'self_claimed': 0.5,
            'social_verified': 0.7,
            'on_chain_verified': 0.9,
            'kyc_verified': 1.0
        }
        
        trust_score = (social_score / 100) * trust_multiplier[verification_level]
        
        return {
            'link_id': link_id,
            'user_id': user_id,
            'wallet_address': wallet_address,
            'network': network,
            'social_score': social_score,
            'verification_level': verification_level,
            'verification_score': self.verification_levels[verification_level],
            'trust_score': round(trust_score * 100, 2),
            'created_timestamp': pd.Timestamp.now().isoformat(),
            'status': 'active'
        }
    
    def process_social_data_with_wallets(self, df: pd.DataFrame, 
                                       generate_mock_wallets: bool = True) -> pd.DataFrame:
        """Process social scoring data and add wallet linking information"""
        
        print(f"Processing wallet links for {len(df)} users...")
        
        wallet_links = []
        
        for idx, row in df.iterrows():
            if idx % 1000 == 0:
                print(f"Processed {idx} wallet links...")
            
            user_id = str(row['_id'])
            social_score = row.get('social_score', 0)
            bio = str(row.get('rawDescription', ''))
            
            # Extract wallets from bio
            extracted_wallets = self.extract_wallet_from_bio(bio)
            
            # If no wallets found and mock generation is enabled
            if not extracted_wallets and generate_mock_wallets:
                # Generate mock wallets based on social score
                num_wallets = 1
                if social_score > 70:
                    num_wallets = random.choice([1, 2, 3])  # High score users might have multiple wallets
                elif social_score > 50:
                    num_wallets = random.choice([1, 2])
                
                networks = ['ethereum', 'bitcoin', 'solana', 'polygon']
                selected_networks = random.sample(networks, min(num_wallets, len(networks)))
                
                for network in selected_networks:
                    mock_address = self.generate_mock_wallet_address(network)
                    verification = 'self_claimed'
                    
                    # Higher social scores get better verification
                    if social_score > 80:
                        verification = random.choice(['social_verified', 'on_chain_verified'])
                    elif social_score > 60:
                        verification = random.choice(['self_claimed', 'social_verified'])
                    
                    link = self.create_social_wallet_link(
                        user_id, social_score, mock_address, network, verification
                    )
                    wallet_links.append(link)
            
            # Process extracted wallets
            for wallet_info in extracted_wallets:
                try:
                    link = self.create_social_wallet_link(
                        user_id, 
                        social_score, 
                        wallet_info['address'], 
                        wallet_info['network'],
                        'social_verified'  # Bio extraction gets social verification
                    )
                    link['extraction_confidence'] = wallet_info['confidence']
                    wallet_links.append(link)
                except ValueError as e:
                    print(f"Skipping invalid wallet for user {user_id}: {e}")
                    continue
        
        # Convert to DataFrame
        wallet_df = pd.DataFrame(wallet_links)
        
        print(f"Created {len(wallet_links)} wallet links")
        return wallet_df
    
    def get_user_wallet_summary(self, wallet_df: pd.DataFrame, user_id: str) -> Dict:
        """Get wallet summary for a specific user"""
        user_wallets = wallet_df[wallet_df['user_id'] == user_id]
        
        if len(user_wallets) == 0:
            return {'user_id': user_id, 'wallet_count': 0, 'networks': [], 'avg_trust_score': 0}
        
        return {
            'user_id': user_id,
            'wallet_count': len(user_wallets),
            'networks': user_wallets['network'].unique().tolist(),
            'avg_trust_score': user_wallets['trust_score'].mean(),
            'max_trust_score': user_wallets['trust_score'].max(),
            'verification_levels': user_wallets['verification_level'].unique().tolist(),
            'wallets': user_wallets.to_dict('records')
        }

def main():
    """Main function to demonstrate wallet linking"""
    # Initialize system
    linking_system = WalletLinkingSystem()
    
    # Load social scoring data
    print("Loading social scoring data...")
    try:
        df = pd.read_csv('twitter_users_with_social_scores.csv')
    except FileNotFoundError:
        print("Social scores file not found. Please run social_scoring_calculator.py first.")
        return
    
    print(f"Loaded {len(df)} users with social scores")
    
    # Process wallet linking
    wallet_df = linking_system.process_social_data_with_wallets(df, generate_mock_wallets=True)
    
    # Save wallet links
    wallet_output = 'social_wallet_links.csv'
    wallet_df.to_csv(wallet_output, index=False)
    print(f"Wallet links saved to {wallet_output}")
    
    # Create combined dataset
    # Group wallet data by user_id to merge with social data
    wallet_summary = wallet_df.groupby('user_id').agg({
        'wallet_address': 'count',
        'network': lambda x: ','.join(x.unique()),
        'trust_score': 'mean',
        'verification_level': lambda x: ','.join(x.unique())
    }).rename(columns={
        'wallet_address': 'wallet_count',
        'network': 'networks',
        'trust_score': 'avg_trust_score',
        'verification_level': 'verification_levels'
    }).reset_index()
    
    # Convert _id to string to match user_id type
    df['_id'] = df['_id'].astype(str)

    # Merge with original social data
    combined_df = df.merge(wallet_summary, left_on='_id', right_on='user_id', how='left')
    combined_df = combined_df.drop('user_id', axis=1)
    
    # Fill NaN values for users without wallets
    combined_df['wallet_count'] = combined_df['wallet_count'].fillna(0)
    combined_df['avg_trust_score'] = combined_df['avg_trust_score'].fillna(0)
    
    # Save combined dataset
    combined_output = 'social_scores_with_wallets.csv'
    combined_df.to_csv(combined_output, index=False)
    print(f"Combined dataset saved to {combined_output}")
    
    # Display statistics
    print(f"\nWallet Linking Statistics:")
    print(f"Total users: {len(df)}")
    print(f"Users with wallets: {len(wallet_df['user_id'].unique())}")
    print(f"Total wallet links: {len(wallet_df)}")
    print(f"Average wallets per user: {len(wallet_df) / len(wallet_df['user_id'].unique()):.2f}")
    
    print(f"\nNetwork Distribution:")
    print(wallet_df['network'].value_counts())
    
    print(f"\nVerification Level Distribution:")
    print(wallet_df['verification_level'].value_counts())
    
    print(f"\nTrust Score Statistics:")
    print(f"Average: {wallet_df['trust_score'].mean():.2f}")
    print(f"Median: {wallet_df['trust_score'].median():.2f}")
    print(f"Max: {wallet_df['trust_score'].max():.2f}")

if __name__ == "__main__":
    main()
